import express from 'express';
import axios from 'axios';
import { Telegraf } from 'telegraf';
import dotenv, { config } from 'dotenv';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import cluster from 'cluster';
import os from 'os';
import sharp from 'sharp';
import cors from 'cors';
import ffmpeg from 'fluent-ffmpeg';

config();

const app = express();
const PORT = 3001;
const CACHE_DIR = path.join(__dirname, 'cache');


// 确保缓存目录存在
(async () => {
    try {
        await fs.promises.access(CACHE_DIR, fs.constants.F_OK);
    } catch (err) {
        await fs.promises.mkdir(CACHE_DIR, { recursive: true });
        console.log('缓存目录已创建:', CACHE_DIR);
    }
})();

// 清理缓存文件夹的函数
async function cleanCacheDirectory() {
    console.log('开始清理缓存文件夹...');
    const now = new Date().getTime();
    const threeDaysAgo = now - (3 * 24 * 60 * 60 * 1000); // 3天前的时间戳

    try {
        const files = await fs.promises.readdir(CACHE_DIR);
        let deletedCount = 0;
        let totalSize = 0;

        // 使用Promise.all等待所有文件处理完成
        await Promise.all(files.map(async (file) => {
            const filePath = path.join(CACHE_DIR, file);
            try {
                const stats = await fs.promises.stat(filePath);

                // 检查文件创建时间是否超过3天
                if (stats.birthtime.getTime() < threeDaysAgo) {
                    await fs.promises.unlink(filePath);
                    deletedCount++;
                    totalSize += stats.size;
                    console.log(`已删除过期缓存文件: ${filePath}`);
                }
            } catch (err) {
                console.error(`处理文件失败: ${filePath}`, err);
            }
        }));

        console.log(`缓存清理完成，共删除了 ${deletedCount} 个文件，释放了约 ${(totalSize / (1024 * 1024)).toFixed(2)} MB 空间`);
    } catch (err) {
        console.error('清理缓存目录失败:', err);
    }
}

// 设置每天凌晨3点执行清理任务
function scheduleCacheCleanup() {
    const now = new Date();
    const night = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1, // 明天
        3, // 凌晨3点
        0, // 0分
        0  // 0秒
    );

    const msToMidnight = night.getTime() - now.getTime();

    // 设置定时器，在指定时间执行清理
    setTimeout(async () => {
        await cleanCacheDirectory();
        // 设置下一次清理任务
        scheduleCacheCleanup();
    }, msToMidnight);

    console.log(`下次缓存清理任务将在 ${new Date(night).toLocaleString()} 执行`);
}

// Telegram Bot 设置
const botToken1 = process.env.BOT_TOKEN_1;
const botToken2 = process.env.BOT_TOKEN_2;
const botToken3 = process.env.BOT_TOKEN_3;
const botToken4 = process.env.BOT_TOKEN_4;
const botToken5 = process.env.BOT_TOKEN_5;
const botToken6 = process.env.BOT_TOKEN_6;

const bots = [botToken1, botToken2, botToken3, botToken4, botToken5, botToken6]
    .filter((token): token is string => {
        if (token === undefined) {
            console.warn('One or more BOT tokens are undefined. Please check your .env file.');
            return false;
        }
        return true;
    })
    .map(token => {
        try {
            return new Telegraf(token);
        } catch (error) {
            console.error(`Failed to instantiate Telegraf bot with token: ${token}. Error:`, error);
            return null;
        }
    })
    .filter((bot): bot is Telegraf => bot !== null);

if (bots.length === 0) {
    throw new Error('No BOTs were successfully instantiated.');
}



app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Range', 'If-None-Match'],
  exposedHeaders: ['Content-Range', 'Accept-Ranges', 'Content-Length', 'Content-Type', 'ETag'],
  credentials: false,
  maxAge: 86400
}));

// 新的代理路由
app.get('/proxy2/:fileid', async (req, res) => {
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    console.log('Received request for file:', req.params.fileid);
    try {
        const fileIdWithExtension = req.params.fileid;
        const fileId = fileIdWithExtension.slice(0, fileIdWithExtension.lastIndexOf("."));

        // 检查文件是否已缓存
        const cachePath = path.join(CACHE_DIR, fileIdWithExtension);

        // 使用异步方法检查文件是否存在
        try {
            await fs.promises.access(cachePath, fs.constants.F_OK);
            console.log('File found in cache:', fileIdWithExtension);

            // 获取文件类型
            let contentType = 'application/octet-stream';
            if (fileIdWithExtension.endsWith('.jpg') || fileIdWithExtension.endsWith('.jpeg')) {
                contentType = 'image/jpeg';
            } else if (fileIdWithExtension.endsWith('.mp4')) {
                contentType = 'video/mp4';
            }

            // 异步获取文件状态
            const fileStats = await fs.promises.stat(cachePath);

            // 生成ETag (基于文件大小和修改时间)
            const etag = `W/"${fileStats.size}-${fileStats.mtime.getTime()}"`;

            // 设置缓存相关的响应头
            res.setHeader('ETag', etag);
            res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
            res.setHeader('Last-Modified', fileStats.mtime.toUTCString());

            // 检查客户端缓存
            const ifNoneMatch = req.headers['if-none-match'];
            const ifModifiedSince = req.headers['if-modified-since'];

            // 如果ETag匹配或者文件未被修改，返回304
            if (ifNoneMatch === etag ||
                (ifModifiedSince && new Date(ifModifiedSince) >= fileStats.mtime)) {
                return res.status(304).end();
            }

            // 设置内容类型和长度
            res.setHeader('Content-Type', contentType);
            res.setHeader('Content-Length', fileStats.size);

            // 使用流返回文件内容
            fs.createReadStream(cachePath).pipe(res);
            return;
        } catch (err) {
            // 文件不存在，继续处理
        }

        // 文件不存在，从Telegram获取
        let file: any;
        let botUsed: any;

        // 使用map和Promise.all替代for循环
        const fileResults = await Promise.all(
            bots.map(async (bot) => {
                try {
                    const result = await bot.telegram.getFile(fileId);
                    if (result.file_path) {
                        return { file: result, bot };
                    }
                    return null;
                } catch (error) {
                    return null;
                }
            })
        );

        // 过滤出成功的结果
        const successResult = fileResults.find(result => result !== null);
        if (successResult) {
            file = successResult.file;
            botUsed = successResult.bot;
        }

        if (!file || !file.file_path || !botUsed) {
            console.error('File path is undefined for file id:', fileId);
            return res.status(500).send('Error retrieving file path.');
        }

        const Original_file_Url = `https://api.telegram.org/file/bot${botUsed.token}/${file.file_path}`;
        const fileResponse = await axios.get(Original_file_Url, {
            responseType: 'stream',
        });

        let contentType = 'application/octet-stream';
        if (file.file_path.includes('photos/')) {
            contentType = 'image/jpeg';
        } else if (file.file_path.includes('videos/')) {
            contentType = 'video/mp4';
        }

        // 对图片进行处理，保存到缓存并返回
        if (contentType === 'image/jpeg') {
            // 使用pipeline处理流，这样更安全也更高效
            const imageProcessing = sharp()
                .jpeg({ quality: 30 });

            // 使用流式处理并行同时写入缓存和发送响应
            let compressedSize = 0;
            const compressedChunks: Buffer[] = [];

            imageProcessing.on('data', (chunk) => {
                compressedChunks.push(chunk);
                compressedSize += chunk.length;
            });

            imageProcessing.on('end', async () => {
                const compressedBuffer = Buffer.concat(compressedChunks, compressedSize);

                // 生成ETag (基于内容哈希)
                const etag = `W/"${crypto.createHash('md5').update(compressedBuffer).digest('hex')}"`;

                // 设置缓存相关的响应头
                res.setHeader('ETag', etag);
                res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
                res.setHeader('Last-Modified', new Date().toUTCString());

                // 设置响应头并返回图片
                res.setHeader('Content-Type', contentType);
                res.setHeader('Content-Length', compressedSize);
                res.end(compressedBuffer);

                // 完成后存储到缓存，不阻塞响应
                try {
                    await fs.promises.writeFile(cachePath, compressedBuffer);
                    console.log('Image saved to cache:', fileIdWithExtension);
                } catch (err) {
                    console.error('Error saving image to cache:', err);
                }
            });

            // 将Telegram的响应流通过sharp处理
            fileResponse.data.pipe(imageProcessing);
        } else {
            // 对于非图片文件，直接通过流传输，避免加载整个文件到内存
            res.setHeader('Content-Type', contentType);
            res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天

            // 创建写入缓存的流
            const cacheWriteStream = fs.createWriteStream(cachePath);

            // 设置流完成和错误事件
            cacheWriteStream.on('finish', () => {
                console.log('File saved to cache:', fileIdWithExtension);
            });

            cacheWriteStream.on('error', (err) => {
                console.error('Error writing file to cache:', err);
                // 错误不会影响响应，因为我们使用了两个独立的管道
            });

            // 设置响应流的错误处理
            res.on('error', (err) => {
                console.error('Error in response stream:', err);
            });

            // 从Telegram获取的响应流复制到两个目标：缓存和HTTP响应
            fileResponse.data.pipe(cacheWriteStream);
            fileResponse.data.pipe(res);
        }

    } catch (error: any) {
        console.error('Error proxying the file:', error);
        res.status(500).send('Error proxying the file: ' + error.message);
    }
});

// 添加函数：提取视频第一帧作为预览图
async function extractVideoThumbnail(videoPath: string, outputPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
        ffmpeg(videoPath)
            .screenshots({
                count: 1,
                folder: path.dirname(outputPath),
                filename: path.basename(outputPath),
                timestamps: ['00:00:01'], // 从视频1秒处截图，避开黑屏
                size: '320x?', // 宽度320，高度按比例自适应
            })
            .on('end', () => {
                console.log('Video thumbnail extracted:', outputPath);
                resolve(outputPath);
            })
            .on('error', (err) => {
                console.error('Error extracting video thumbnail:', err);
                reject(err);
            });
    });
}

// 在app.get('/file/:fileId')之后，添加新的路由处理视频预览图请求
app.get('/thumbnail/:fileId', async (req, res) => {
    try {
        const { fileId } = req.params;

        // 检查文件ID是否有效
        if (!fileId) {
            return res.status(400).send('Invalid file ID');
        }

        // 构建视频文件和预览图的缓存路径
        const fileIdWithExtension = `${fileId}.mp4`;
        const videoPath = path.join(CACHE_DIR, fileIdWithExtension);
        const thumbnailPath = path.join(CACHE_DIR, `${fileId}_thumb.jpg`);

        // 检查预览图是否已存在于缓存中
        try {
            await fs.promises.access(thumbnailPath, fs.constants.F_OK);
            console.log('Thumbnail found in cache:', thumbnailPath);

            // 异步获取文件状态
            const thumbStats = await fs.promises.stat(thumbnailPath);

            // 生成ETag (基于文件大小和修改时间)
            const etag = `W/"${thumbStats.size}-${thumbStats.mtime.getTime()}"`;

            // 设置缓存相关的响应头
            res.setHeader('ETag', etag);
            res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
            res.setHeader('Last-Modified', thumbStats.mtime.toUTCString());

            // 检查客户端缓存
            const ifNoneMatch = req.headers['if-none-match'];
            const ifModifiedSince = req.headers['if-modified-since'];

            // 如果ETag匹配或者文件未被修改，返回304
            if (ifNoneMatch === etag ||
                (ifModifiedSince && new Date(ifModifiedSince) >= thumbStats.mtime)) {
                return res.status(304).end();
            }

            res.setHeader('Content-Type', 'image/jpeg');
            res.setHeader('Content-Length', thumbStats.size);
            fs.createReadStream(thumbnailPath).pipe(res);
            return;
        } catch (err) {
            // 缩略图不存在，继续处理
        }

        // 检查视频是否已存在于缓存中
        try {
            await fs.promises.access(videoPath, fs.constants.F_OK);
            console.log('Video found in cache, extracting thumbnail:', videoPath);

            // 检查视频文件是否可读且大小合理
            const videoStats = await fs.promises.stat(videoPath);
            if (videoStats.size === 0) {
                console.error('缓存的视频文件大小为0，可能已损坏');
                throw new Error('视频文件可能已损坏');
            }

            // 提取缩略图
            try {
                await extractVideoThumbnail(videoPath, thumbnailPath);
            } catch (thumbErr) {
                console.error('从缓存视频提取缩略图失败:', thumbErr);
                throw new Error('无法从视频提取缩略图');
            }

            const thumbStats = await fs.promises.stat(thumbnailPath);

            // 生成ETag (基于文件大小和修改时间)
            const etag = `W/"${thumbStats.size}-${thumbStats.mtime.getTime()}"`;

            // 设置缓存相关的响应头
            res.setHeader('ETag', etag);
            res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
            res.setHeader('Last-Modified', thumbStats.mtime.toUTCString());

            res.setHeader('Content-Type', 'image/jpeg');
            res.setHeader('Content-Length', thumbStats.size);
            fs.createReadStream(thumbnailPath).pipe(res);
            return;
        } catch (err: any) {
            // 视频不存在或处理失败，继续处理
            console.log('缓存视频处理失败或不存在，将从Telegram获取:', err.message || '未知错误');
        }

        // 视频不在缓存中，先获取视频然后提取缩略图
        let file: any;
        let botUsed: any;

        // 使用map和Promise.all替代for循环
        const fileResults = await Promise.all(
            bots.map(async (bot) => {
                try {
                    const result = await bot.telegram.getFile(fileId);
                    if (result.file_path) {
                        return { file: result, bot };
                    }
                    return null;
                } catch (error) {
                    return null;
                }
            })
        );

        // 过滤出成功的结果
        const successResult = fileResults.find(result => result !== null);
        if (successResult) {
            file = successResult.file;
            botUsed = successResult.bot;
        }

        if (!file || !file.file_path || !botUsed) {
            console.error('File path is undefined for file id:', fileId);
            return res.status(500).send('Error retrieving file path.');
        }

        // 确保是视频文件
        if (!file.file_path.includes('videos/')) {
            return res.status(400).send('File is not a video.');
        }

        const Original_file_Url = `https://api.telegram.org/file/bot${botUsed.token}/${file.file_path}`;

        // 先下载视频到临时文件
        console.log('Downloading video for thumbnail extraction:', fileId);
        const videoResponse = await axios.get(Original_file_Url, {
            responseType: 'stream',
        });

        // 创建写入视频的流
        const videoWriteStream = fs.createWriteStream(videoPath);
        videoResponse.data.pipe(videoWriteStream);

        // 等待视频下载完成
        await new Promise<void>((resolve, reject) => {
            videoWriteStream.on('finish', () => resolve());
            videoWriteStream.on('error', reject);
        });

        // 确保文件已完全写入并可访问
        try {
            // 等待一小段时间确保文件系统已完成写入
            await new Promise(resolve => setTimeout(resolve, 500));

            // 检查文件是否存在且可读
            await fs.promises.access(videoPath, fs.constants.R_OK);

            // 获取文件状态以确保文件大小合理
            const videoStats = await fs.promises.stat(videoPath);
            if (videoStats.size === 0) {
                throw new Error('视频文件大小为0，可能下载失败');
            }

            // 提取视频缩略图
            await extractVideoThumbnail(videoPath, thumbnailPath);
        } catch (err: any) {
            console.error('视频文件处理失败:', err);
            throw new Error(`无法处理视频文件: ${err.message || '未知错误'}`);
        }

        // 返回缩略图
        const thumbStats = await fs.promises.stat(thumbnailPath);

        // 生成ETag (基于文件大小和修改时间)
        const etag = `W/"${thumbStats.size}-${thumbStats.mtime.getTime()}"`;

        // 设置缓存相关的响应头
        res.setHeader('ETag', etag);
        res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
        res.setHeader('Last-Modified', thumbStats.mtime.toUTCString());

        res.setHeader('Content-Type', 'image/jpeg');
        res.setHeader('Content-Length', thumbStats.size);
        fs.createReadStream(thumbnailPath).pipe(res);

    } catch (error: any) {
        console.error('Error generating video thumbnail:', error);
        res.status(500).send('Error generating video thumbnail: ' + error.message);
    }
});

// 缓存目录已在文件开头检查

// 将Express应用启动代码改为集群模式
if (cluster.isPrimary) {
    // 获取CPU核心数
    const numCPUs = os.cpus().length;

    console.log(`主进程 ${process.pid} 正在运行`);
    console.log(`启动 ${numCPUs} 个工作进程...`);

    // 启动缓存清理调度（只在主进程中执行）
    scheduleCacheCleanup();

    // 为每个CPU核心创建一个工作进程
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }

    // 监听工作进程退出事件，并重启
    cluster.on('exit', (worker) => {
        console.log(`工作进程 ${worker.process.pid} 已退出，正在重启...`);
        cluster.fork();
    });
} else {
    // 工作进程运行Express应用
    app.listen(PORT, () => {
        console.log(`工作进程 ${process.pid} 监听端口 ${PORT}`);
    });
}