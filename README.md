# Telegram Media Reverse Proxy

这个服务提供了从Telegram获取媒体文件的功能。

## 主要功能

### `/proxy/:fileid` 接口

这是主要接口，用于获取Telegram上的媒体文件，并直接返回文件内容。

#### 工作流程

1. 客户端请求 `/proxy/:fileid`（例如：`/proxy/abc123.jpg`）
2. 服务器从Telegram获取文件
3. 对于图片类型，服务器会进行压缩处理
4. 服务器直接将文件内容返回给客户端

#### 示例请求

```
GET /proxy/abc123.jpg
```

#### 成功响应

服务器直接返回文件内容（二进制数据），并设置适当的`Content-Type`头。例如：

- 图片文件：`Content-Type: image/jpeg`
- 视频文件：`Content-Type: video/mp4`
- 其他文件：`Content-Type: application/octet-stream`

#### 在HTML中使用示例

可以直接在`<img>`标签中使用该接口：

```html
<img src="http://your-server:3005/proxy/abc123.jpg" alt="Telegram图片" />
```

或在CSS中设置背景图片：

```css
.element {
  background-image: url('http://your-server:3005/proxy/abc123.jpg');
}
```

### 其他接口

- `/clear-cache`：清除所有已下载的文件
- `/cache-stats`：查看已下载文件的统计信息

## 安装与运行

1. 确保已安装Node.js
2. 创建`.env`文件，添加Telegram Bot tokens：
```
BOT_TOKEN_1=your_token1
BOT_TOKEN_2=your_token2
...
```
3. 安装依赖：`npm install`
4. 运行服务：`npm start`或`node index.js`

## 注意事项

- 服务默认监听3005端口
- 每次请求都会从Telegram重新下载文件

## 更新记录

### 最近更新

- 修改了`/proxy`端点，现在直接返回文件内容，而不是静态URL
- 移除了缓存检查功能，每次请求都会重新从Telegram下载文件
- 禁用了自动缓存清理功能 