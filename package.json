{"name": "tg_mediareverseproxy_gen2", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@types/express": "^4.17.18", "@types/fluent-ffmpeg": "^2.1.26", "@types/node": "^20.8.4", "@types/sharp": "^0.32.0", "axios": "^1.5.1", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.3", "lru-cache": "^11.0.0", "sharp": "^0.33.5", "telegraf": "^4.15.0", "typescript": "^5.2.2"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/lru-cache": "^7.10.10"}, "keywords": [], "author": "", "license": "ISC"}